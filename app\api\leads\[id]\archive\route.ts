import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ActivityLogger } from '@/lib/activity-logger'
import { Role } from '@prisma/client'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if lead exists
    const lead = await prisma.lead.findUnique({
      where: { id },
      include: {
        assignedGroup: {
          include: {
            course: { select: { name: true, level: true } },
            teacher: { include: { user: { select: { name: true } } } }
          }
        }
      }
    })

    if (!lead) {
      return NextResponse.json({ error: 'Lead not found' }, { status: 404 })
    }

    if (lead.status !== 'GROUP_ASSIGNED') {
      return NextResponse.json(
        { error: 'Only leads with assigned groups can be archived' },
        { status: 400 }
      )
    }

    if (lead.archivedAt) {
      return NextResponse.json({ error: 'Lead is already archived' }, { status: 400 })
    }

    const now = new Date()

    // Archive the lead
    const archivedLead = await prisma.lead.update({
      where: { id },
      data: {
        status: 'ARCHIVED',
        archivedAt: now,
        updatedAt: now,
      },
      include: {
        assignedGroup: {
          include: {
            course: { select: { name: true, level: true } },
            teacher: { include: { user: { select: { name: true } } } }
          }
        },
        assignedTeacher: {
          include: { user: { select: { name: true } } }
        },
        callRecords: {
          orderBy: { createdAt: 'desc' },
          take: 1
        }
      }
    })

    // Log activity
    await ActivityLogger.logLeadContacted(
      session.user.id,
      session.user.role as Role,
      lead.id,
      {
        leadName: lead.name,
        leadPhone: lead.phone,
        previousStatus: lead.status,
        newStatus: 'ARCHIVED',
        notes: `Lead archived after group assignment to: ${lead.assignedGroup?.name}`,
      },
      request
    )

    return NextResponse.json({ lead: archivedLead })
  } catch (error) {
    console.error('Error archiving lead:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Unarchive a lead (for admin purposes)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Only admins can unarchive
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Only admins can unarchive leads' }, { status: 403 })
    }

    // Check if lead exists and is archived
    const lead = await prisma.lead.findUnique({
      where: { id },
    })

    if (!lead) {
      return NextResponse.json({ error: 'Lead not found' }, { status: 404 })
    }

    if (!lead.archivedAt) {
      return NextResponse.json({ error: 'Lead is not archived' }, { status: 400 })
    }

    const now = new Date()

    // Unarchive the lead
    const unarchivedLead = await prisma.lead.update({
      where: { id },
      data: {
        status: 'GROUP_ASSIGNED', // Return to previous status
        archivedAt: null,
        updatedAt: now,
      },
      include: {
        assignedGroup: {
          include: {
            course: { select: { name: true, level: true } },
            teacher: { include: { user: { select: { name: true } } } }
          }
        },
        assignedTeacher: {
          include: { user: { select: { name: true } } }
        }
      }
    })

    // Log activity
    await ActivityLogger.logLeadContacted(
      session.user.id,
      session.user.role as Role,
      lead.id,
      {
        leadName: lead.name,
        leadPhone: lead.phone,
        previousStatus: 'ARCHIVED',
        newStatus: 'GROUP_ASSIGNED',
        notes: 'Lead unarchived by admin',
      },
      request
    )

    return NextResponse.json({ lead: unarchivedLead })
  } catch (error) {
    console.error('Error unarchiving lead:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
