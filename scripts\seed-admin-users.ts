import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🚀 Adding admin users to management database...')

  // Create Admin user for Management Server
  const adminPassword = await bcrypt.hash('admin123', 10)
  const adminUser = await prisma.user.upsert({
    where: { phone: '+998901234567' },
    update: {
      password: adminPassword,
      role: 'ADMIN'
    },
    create: {
      phone: '+998901234567',
      name: 'Admin User',
      email: '<EMAIL>',
      role: 'ADMIN',
      password: adminPassword,
    },
  })

  // Create Parviz - Super Admin
  const parvizPassword = await bcrypt.hash('Parviz0106$', 10)
  const parvizUser = await prisma.user.upsert({
    where: { phone: '+998906006299' },
    update: {
      password: parvizPassword,
      role: '<PERSON><PERSON><PERSON>'
    },
    create: {
      phone: '+998906006299',
      name: '<PERSON>r<PERSON><PERSON>',
      email: '<EMAIL>',
      role: '<PERSON><PERSON><PERSON>',
      password: parvizPassword,
    },
  })

  // Create Cashier user for Management Server
  const cashierPassword = await bcrypt.hash('cashier123', 10)
  const cashierUser = await prisma.user.upsert({
    where: { phone: '+998901234570' },
    update: {
      password: cashierPassword,
      role: 'CASHIER'
    },
    create: {
      phone: '+998901234570',
      name: 'Cashier User',
      email: '<EMAIL>',
      role: 'CASHIER',
      password: cashierPassword,
    },
  })

  // Update existing Manager to ensure it exists
  const managerPassword = await bcrypt.hash('manager123', 10)
  const managerUser = await prisma.user.upsert({
    where: { phone: '+998901234568' },
    update: {
      password: managerPassword,
      role: 'MANAGER'
    },
    create: {
      phone: '+998901234568',
      name: 'Manager User',
      email: '<EMAIL>',
      role: 'MANAGER',
      password: managerPassword,
    },
  })

  console.log('✅ Admin users added successfully!')
  console.log('\n🔐 MANAGEMENT SERVER CREDENTIALS:')
  console.log('   Admin: +998901234567 / admin123')
  console.log('   Parviz (Super Admin): +998906006299 / Parviz0106$')
  console.log('   Manager: +998901234568 / manager123')
  console.log('   Cashier: +998901234570 / cashier123')
  
  console.log('\n🎉 Management database is ready for testing!')
}

main()
  .catch((e) => {
    console.error('❌ Error adding admin users:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
