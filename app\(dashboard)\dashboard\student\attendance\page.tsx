'use client'

import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { formatDate } from '@/lib/utils'
import { Search, Calendar, CheckCircle, XCircle, Clock, AlertCircle, Loader2, TrendingUp } from 'lucide-react'
import { useSession } from 'next-auth/react'

interface Attendance {
  id: string
  status: 'PRESENT' | 'ABSENT' | 'LATE' | 'EXCUSED'
  notes: string | null
  createdAt: string
  class: {
    id: string
    date: string
    topic: string | null
    group: {
      id: string
      name: string
      course: {
        name: string
      }
    }
    teacher: {
      user: {
        name: string
      }
    }
  }
}

export default function StudentAttendancePage() {
  const { data: session } = useSession()
  const [attendances, setAttendances] = useState<Attendance[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('ALL')
  const [error, setError] = useState<string | null>(null)

  const fetchAttendances = useCallback(async () => {
    try {
      setLoading(true)
      let url = `/api/students/${session?.user?.id}/attendance?limit=100`

      if (statusFilter !== 'ALL') {
        url += `&status=${statusFilter}`
      }

      const response = await fetch(url)
      const data = await response.json()
      setAttendances(data.attendances || [])
      setError(null)
    } catch (error) {
      console.error('Error fetching attendances:', error)
      setError('Failed to fetch attendance records')
    } finally {
      setLoading(false)
    }
  }, [session?.user?.id, statusFilter])

  useEffect(() => {
    if (session?.user?.id) {
      fetchAttendances()
    }
  }, [session?.user?.id, statusFilter, fetchAttendances])

  const filteredAttendances = attendances.filter(attendance =>
    attendance.class.group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    attendance.class.group.course.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    attendance.class.teacher.user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (attendance.class.topic && attendance.class.topic.toLowerCase().includes(searchTerm.toLowerCase()))
  )

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PRESENT':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'ABSENT':
        return <XCircle className="h-4 w-4 text-red-600" />
      case 'LATE':
        return <Clock className="h-4 w-4 text-yellow-600" />
      case 'EXCUSED':
        return <AlertCircle className="h-4 w-4 text-blue-600" />
      default:
        return null
    }
  }

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'PRESENT':
        return 'bg-green-100 text-green-800'
      case 'ABSENT':
        return 'bg-red-100 text-red-800'
      case 'LATE':
        return 'bg-yellow-100 text-yellow-800'
      case 'EXCUSED':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  // Calculate attendance statistics
  const totalClasses = attendances.length
  const presentClasses = attendances.filter(a => a.status === 'PRESENT').length
  const lateClasses = attendances.filter(a => a.status === 'LATE').length
  const absentClasses = attendances.filter(a => a.status === 'ABSENT').length
  const excusedClasses = attendances.filter(a => a.status === 'EXCUSED').length
  const attendanceRate = totalClasses > 0 ? Math.round((presentClasses / totalClasses) * 100) : 0

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading attendance records...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">My Attendance</h1>
          <p className="text-gray-600">View your class attendance history</p>
        </div>
      </div>

      {/* Attendance Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <TrendingUp className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Attendance Rate</p>
                <p className="text-2xl font-bold text-gray-900">{attendanceRate}%</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Present</p>
                <p className="text-2xl font-bold text-gray-900">{presentClasses}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-yellow-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Late</p>
                <p className="text-2xl font-bold text-gray-900">{lateClasses}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <XCircle className="h-8 w-8 text-red-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Absent</p>
                <p className="text-2xl font-bold text-gray-900">{absentClasses}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <AlertCircle className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Excused</p>
                <p className="text-2xl font-bold text-gray-900">{excusedClasses}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filter Attendance</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search by group, course, teacher, or topic..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              {['ALL', 'PRESENT', 'ABSENT', 'LATE', 'EXCUSED'].map((status) => (
                <button
                  key={status}
                  onClick={() => setStatusFilter(status)}
                  className={`px-3 py-2 text-sm rounded-md transition-colors ${
                    statusFilter === status
                      ? 'bg-blue-100 text-blue-700 border border-blue-300'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {status === 'ALL' ? 'All' : status.charAt(0) + status.slice(1).toLowerCase()}
                </button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Attendance Table */}
      <Card>
        <CardHeader>
          <CardTitle>Attendance History ({filteredAttendances.length})</CardTitle>
          <CardDescription>
            Your complete attendance record
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date</TableHead>
                <TableHead>Group</TableHead>
                <TableHead>Course</TableHead>
                <TableHead>Teacher</TableHead>
                <TableHead>Topic</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Notes</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredAttendances.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    No attendance records found
                  </TableCell>
                </TableRow>
              ) : (
                filteredAttendances.map((attendance) => (
                  <TableRow key={attendance.id}>
                    <TableCell>
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                        {formatDate(new Date(attendance.class.date))}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{attendance.class.group.name}</Badge>
                    </TableCell>
                    <TableCell>{attendance.class.group.course.name}</TableCell>
                    <TableCell>{attendance.class.teacher.user.name}</TableCell>
                    <TableCell>{attendance.class.topic || "N/A"}</TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(attendance.status)}
                        <Badge className={getStatusBadgeVariant(attendance.status)}>
                          {attendance.status}
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell>{attendance.notes || "N/A"}</TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
