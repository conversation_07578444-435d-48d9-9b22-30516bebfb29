# Staff Server Setup Summary

## Overview
This document summarizes the setup and configuration of the Innovative Centre CRM Staff Server - a separate deployment for reception and teaching staff with limited functionality.

## Architecture
- **Admin Server**: Handles ADMIN and CASHIER roles with full system access
- **Staff Server**: Handles RECEPTION, ACADEMIC_MANAGER, TEACHER, MANAGER, and STUDENT roles with limited functionality

## Database Configuration
- **Database**: PostgreSQL (Neon)
- **Connection String**: `postgresql://crm-staff_owner:<EMAIL>/crm-staff?sslmode=require&channel_binding=require`
- **Schema**: Identical to admin server but separate database instance

## Role Access Matrix (Staff Server)

### MANAGER
- Full access to all staff server functionality
- Can manage users, teachers, students, groups, enrollments
- Access to attendance, assessments, leads, communication

### TEACHER
- Access to groups, students, attendance, assessments, communication
- Limited to their assigned classes and students

### RECEPTION
- Access to leads, students, enrollments, communication
- Primary interface for student registration and inquiries

### ACADEMIC_MANAGER
- Access to assessments, students
- Focus on academic performance and testing

### STUDENT
- Access to their own dashboard with assignments, attendance, progress, payments, schedule

## Removed Features (Admin/Cashier Only)
- Analytics dashboard
- Financial reports
- Payment management
- KPI tracking
- Activity logs
- Admin user management

## Removed API Endpoints
- `/api/analytics`
- `/api/reports`
- `/api/payments`
- `/api/kpis`
- `/api/activity-logs`
- `/api/teachers/kpis`

## Removed Dashboard Pages
- `/dashboard/admin/*`
- `/dashboard/analytics`
- `/dashboard/payments`

## Security Features
- Middleware blocks ADMIN and CASHIER roles from accessing staff server
- Role-based route protection for all endpoints
- Separate database prevents data access conflicts

## Development Setup
1. Database schema pushed to staff database
2. Dependencies installed
3. Environment variables configured
4. Server runs on port 3001 (to avoid conflicts with admin server on 3000)

## Next Steps
1. Deploy staff server to separate hosting environment
2. Set up API communication between admin and staff servers
3. Implement data synchronization mechanisms
4. Configure IP restrictions for admin server
5. Set up monitoring and logging for both servers

## Communication Between Servers
- Staff server will communicate with admin server via secure API endpoints
- Admin server will handle financial data and high-level analytics
- Staff server will handle day-to-day operations and student management

## Deployment Considerations
- Staff server should be deployed to a different domain/subdomain
- Environment variables need to be configured for production
- Database connections should use production credentials
- CORS settings may need adjustment for inter-server communication
