import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ActivityLogger } from '@/lib/activity-logger'
import { Role } from '@prisma/client'
import * as z from 'zod'

const classSchema = z.object({
  groupId: z.string().min(1, 'Group is required'),
  teacherId: z.string().min(1, 'Teacher is required'),
  date: z.string().min(1, 'Date is required'),
  topic: z.string().optional(),
  homework: z.string().optional(),
  notes: z.string().optional(),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const groupId = searchParams.get('groupId')
    const teacherId = searchParams.get('teacherId')
    const dateFrom = searchParams.get('dateFrom')
    const dateTo = searchParams.get('dateTo')

    const where: any = {}

    if (groupId) {
      where.groupId = groupId
    }

    if (teacherId) {
      where.teacherId = teacherId
    }

    if (dateFrom || dateTo) {
      where.date = {}
      if (dateFrom) {
        where.date.gte = new Date(dateFrom)
      }
      if (dateTo) {
        where.date.lte = new Date(dateTo)
      }
    }

    const [classes, total] = await Promise.all([
      prisma.class.findMany({
        where,
        include: {
          group: {
            include: {
              course: {
                select: {
                  name: true,
                  level: true,
                },
              },
            },
          },
          teacher: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
          _count: {
            select: {
              attendances: true,
            },
          },
        },
        orderBy: { date: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.class.count({ where }),
    ])

    return NextResponse.json({
      classes,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching classes:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Only teachers, admins, and managers can create classes
    if (!session.user.role || !['ADMIN', 'MANAGER', 'TEACHER'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const body = await request.json()
    const validatedData = classSchema.parse(body)

    // Check if group exists
    const group = await prisma.group.findUnique({
      where: { id: validatedData.groupId },
      include: {
        course: {
          select: {
            name: true,
            level: true,
          },
        },
      },
    })

    if (!group) {
      return NextResponse.json(
        { error: 'Group not found' },
        { status: 400 }
      )
    }

    // Check if teacher exists
    const teacher = await prisma.teacher.findUnique({
      where: { id: validatedData.teacherId },
      include: {
        user: {
          select: {
            name: true,
          },
        },
      },
    })

    if (!teacher) {
      return NextResponse.json(
        { error: 'Teacher not found' },
        { status: 400 }
      )
    }

    // Check if class already exists for this group on this date
    const existingClass = await prisma.class.findFirst({
      where: {
        groupId: validatedData.groupId,
        date: new Date(validatedData.date),
      },
    })

    if (existingClass) {
      return NextResponse.json(
        { error: 'A class already exists for this group on this date' },
        { status: 400 }
      )
    }

    const classRecord = await prisma.class.create({
      data: {
        ...validatedData,
        date: new Date(validatedData.date),
      },
      include: {
        group: {
          include: {
            course: {
              select: {
                name: true,
                level: true,
              },
            },
          },
        },
        teacher: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        _count: {
          select: {
            attendances: true,
          },
        },
      },
    })

    // Log the activity
    await ActivityLogger.log({
      userId: session.user.id,
      userRole: session.user.role as Role,
      action: 'CREATE',
      resource: 'class',
      resourceId: classRecord.id,
      details: {
        groupName: classRecord.group.name,
        courseName: classRecord.group.course?.name,
        teacherName: classRecord.teacher.user.name,
        date: classRecord.date.toISOString(),
        topic: classRecord.topic,
      },
      ipAddress: ActivityLogger.getIpAddress(request),
      userAgent: ActivityLogger.getUserAgent(request),
    })

    return NextResponse.json(classRecord, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating class:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
