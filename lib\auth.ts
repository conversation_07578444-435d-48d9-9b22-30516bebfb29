import { NextAuthOptions } from "next-auth"
import Cred<PERSON><PERSON><PERSON><PERSON><PERSON> from "next-auth/providers/credentials"
import { StaffServerAPI } from "@/lib/inter-server"

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        phone: { label: "Phone", type: "text" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.phone || !credentials?.password) {
          return null
        }

        try {
          // Staff server: Authenticate against admin server via inter-server communication
          const authResult = await StaffServerAPI.authenticateUser(
            credentials.phone,
            credentials.password
          )

          if (!authResult.success) {
            console.error('Authentication failed:', authResult.error)
            return null
          }

          const user = authResult.data.user
          if (!user) {
            console.error('No user data returned from admin server')
            return null
          }

          // Verify the user role is allowed on staff server
          const allowedRoles = ['RECEPTION', 'ACADEMIC_MANAGER', 'TEACHER', 'MANAGER']
          if (!allowedRoles.includes(user.role)) {
            console.error('User role not allowed on staff server:', user.role)
            return null
          }

          return {
            id: user.id,
            phone: user.phone,
            name: user.name,
            email: user.email,
            role: user.role,
          }
        } catch (error) {
          console.error('Error authenticating user via inter-server:', error)
          return null
        }
      }
    })
  ],
  session: {
    strategy: "jwt"
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role || null
      }
      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub!
        session.user.role = (token.role as string) || null
      }
      return session
    }
  },
  pages: {
    signIn: "/auth/signin",
  }
}
