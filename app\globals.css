@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Professional CRM Color Palette */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    /* Primary: Professional Blue */
    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 100%;

    /* Secondary: Sophisticated Gray */
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;

    /* Accent: Modern Teal */
    --accent: 173 80% 40%;
    --accent-foreground: 0 0% 100%;

    /* Muted: Elegant Gray */
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;

    /* Success: Professional Green */
    --success: 142 76% 36%;
    --success-foreground: 0 0% 100%;

    /* Warning: Attention Orange */
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 100%;

    /* Destructive: Alert <PERSON> */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    /* UI Elements */
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 217 91% 60%;
    --radius: 0.75rem;

    /* Dashboard Specific */
    --dashboard-bg: 248 250% 99%;
    --sidebar-bg: 222.2 84% 4.9%;
    --sidebar-hover: 217.2 32.6% 17.5%;
    --card-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --card-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
    --dashboard-bg: 222.2 84% 4.9%;
    --sidebar-bg: 222.2 84% 4.9%;
    --sidebar-hover: 217.2 32.6% 17.5%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-sans antialiased;
  }
}

@layer components {
  /* Professional Dashboard Components */
  .dashboard-card {
    @apply bg-white rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200;
  }

  .dashboard-card-header {
    @apply p-6 pb-4;
  }

  .dashboard-card-content {
    @apply p-6 pt-0;
  }

  .kpi-card {
    @apply dashboard-card hover:scale-[1.02] transition-transform duration-200;
  }

  .kpi-value {
    @apply text-3xl font-bold tracking-tight;
  }

  .kpi-label {
    @apply text-sm font-medium text-gray-600 uppercase tracking-wide;
  }

  .kpi-change {
    @apply text-xs font-medium flex items-center gap-1;
  }

  /* Professional Sidebar */
  .sidebar-nav-item {
    @apply flex items-center gap-3 px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200;
  }

  .sidebar-nav-item:hover {
    @apply bg-gray-100 text-gray-900;
  }

  .sidebar-nav-item.active {
    @apply bg-blue-50 text-blue-700 border-r-2 border-blue-700;
  }

  /* Professional Tables */
  .data-table {
    @apply w-full border-collapse;
  }

  .data-table th {
    @apply px-6 py-4 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider border-b border-gray-200;
  }

  .data-table td {
    @apply px-6 py-4 whitespace-nowrap border-b border-gray-100;
  }

  .data-table tr:hover {
    @apply bg-gray-50;
  }

  /* Professional Buttons */
  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white font-medium px-4 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2;
  }

  .btn-secondary {
    @apply bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium px-4 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2;
  }

  .btn-success {
    @apply bg-green-600 hover:bg-green-700 text-white font-medium px-4 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2;
  }

  .btn-danger {
    @apply bg-red-600 hover:bg-red-700 text-white font-medium px-4 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2;
  }

  /* Professional Status Badges */
  .status-badge {
    @apply inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full text-xs font-medium;
  }

  .status-active {
    @apply bg-green-100 text-green-800;
  }

  .status-inactive {
    @apply bg-gray-100 text-gray-800;
  }

  .status-pending {
    @apply bg-yellow-100 text-yellow-800;
  }

  .status-danger {
    @apply bg-red-100 text-red-800;
  }

  /* Professional Form Elements */
  .form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-2;
  }

  /* Professional Animations */
  .fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .scale-in {
    animation: scaleIn 0.2s ease-out;
  }
}

@layer utilities {
  /* Professional Gradients */
  .gradient-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  }

  .gradient-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  }

  .gradient-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  }

  .gradient-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  }

  /* Professional Shadows */
  .shadow-professional {
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  }

  .shadow-professional-lg {
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  }

  /* Professional Text */
  .text-professional {
    @apply text-gray-700 leading-relaxed;
  }

  .text-professional-heading {
    @apply text-gray-900 font-semibold tracking-tight;
  }
}

/* Professional Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Professional Scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
