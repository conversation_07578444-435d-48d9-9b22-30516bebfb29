generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model User {
  id             String         @id @default(cuid())
  email          String?        @unique
  phone          String         @unique
  name           String
  role           Role           @default(STUDENT)
  password       String
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt
  accounts       Account[]
  activityLogs   ActivityLog[]
  announcements  Announcement[]
  callRecords    CallRecord[]
  messages       Message[]
  sessions       Session[]
  studentProfile Student?
  teacherProfile Teacher?

  @@map("users")
}

model Student {
  id                String        @id @default(cuid())
  userId            String        @unique
  level             Level         @default(A1)
  branch            String
  emergencyContact  String?
  photoUrl          String?
  dateOfBirth       DateTime?
  address           String?
  status            StudentStatus @default(ACTIVE)
  currentGroupId    String?
  droppedAt         DateTime?
  pausedAt          DateTime?
  resumedAt         DateTime?
  reEnrollmentNotes String?
  lastContactedAt   DateTime?
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt
  assessments       Assessment[]
  attendances       Attendance[]
  enrollments       Enrollment[]
  payments          Payment[]
  currentGroup      Group?        @relation("StudentCurrentGroup", fields: [currentGroupId], references: [id])
  user              User          @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("students")
}

model Teacher {
  id            String      @id @default(cuid())
  userId        String      @unique
  subject       String
  experience    Int?
  salary        Decimal?
  branch        String
  photoUrl      String?
  tier          TeacherTier @default(NEW)
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
  classes       Class[]
  groups        Group[]
  assignedLeads Lead[]
  user          User        @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("teachers")
}

model Lead {
  id                String       @id @default(cuid())
  name              String
  phone             String       @unique
  coursePreference  String
  status            LeadStatus   @default(NEW)
  source            String?
  notes             String?
  branch            String       @default("main")
  assignedTo        String?
  followUpDate      DateTime?
  callStartedAt     DateTime?
  callEndedAt       DateTime?
  callDuration      Int?
  assignedGroupId   String?
  assignedTeacherId String?
  assignedAt        DateTime?
  archivedAt        DateTime?
  createdAt         DateTime     @default(now())
  updatedAt         DateTime     @updatedAt
  callRecords       CallRecord[]
  assignedGroup     Group?       @relation(fields: [assignedGroupId], references: [id])
  assignedTeacher   Teacher?     @relation(fields: [assignedTeacherId], references: [id])

  @@map("leads")
}

model CallRecord {
  id           String    @id @default(cuid())
  leadId       String
  userId       String
  startedAt    DateTime
  endedAt      DateTime?
  duration     Int?
  notes        String?
  recordingUrl String?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  lead         Lead      @relation(fields: [leadId], references: [id], onDelete: Cascade)
  user         User      @relation(fields: [userId], references: [id])

  @@map("call_records")
}

model Course {
  id          String   @id @default(cuid())
  name        String   @unique
  level       Level
  description String?
  duration    Int
  price       Decimal
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  groups      Group[]

  @@map("courses")
}

model Group {
  id               String            @id @default(cuid())
  name             String            @unique
  courseId         String
  teacherId        String
  capacity         Int               @default(20)
  schedule         String
  room             String?
  cabinetId        String?
  branch           String
  startDate        DateTime
  endDate          DateTime
  isActive         Boolean           @default(true)
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @updatedAt
  assessments      Assessment[]
  cabinetSchedules CabinetSchedule[]
  classes          Class[]
  enrollments      Enrollment[]
  cabinet          Cabinet?          @relation(fields: [cabinetId], references: [id])
  course           Course            @relation(fields: [courseId], references: [id])
  teacher          Teacher           @relation(fields: [teacherId], references: [id])
  assignedLeads    Lead[]
  currentStudents  Student[]         @relation("StudentCurrentGroup")

  @@map("groups")
}

model Cabinet {
  id        String            @id @default(cuid())
  name      String            @unique
  number    String            @unique
  capacity  Int               @default(20)
  floor     Int?
  building  String?
  branch    String
  equipment String?
  isActive  Boolean           @default(true)
  notes     String?
  createdAt DateTime          @default(now())
  updatedAt DateTime          @updatedAt
  schedules CabinetSchedule[]
  groups    Group[]

  @@map("cabinets")
}

model CabinetSchedule {
  id        String   @id @default(cuid())
  cabinetId String
  groupId   String?
  dayOfWeek Int
  startTime String
  endTime   String
  title     String?
  isBlocked Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  cabinet   Cabinet  @relation(fields: [cabinetId], references: [id], onDelete: Cascade)
  group     Group?   @relation(fields: [groupId], references: [id])

  @@map("cabinet_schedules")
}

model Enrollment {
  id        String           @id @default(cuid())
  studentId String
  groupId   String
  status    EnrollmentStatus @default(ACTIVE)
  startDate DateTime
  endDate   DateTime?
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt
  group     Group            @relation(fields: [groupId], references: [id])
  student   Student          @relation(fields: [studentId], references: [id])

  @@unique([studentId, groupId])
  @@map("enrollments")
}

model Class {
  id          String       @id @default(cuid())
  groupId     String
  teacherId   String
  date        DateTime
  topic       String?
  homework    String?
  notes       String?
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  attendances Attendance[]
  group       Group        @relation(fields: [groupId], references: [id])
  teacher     Teacher      @relation(fields: [teacherId], references: [id])

  @@map("classes")
}

model Attendance {
  id        String           @id @default(cuid())
  studentId String
  classId   String
  status    AttendanceStatus @default(PRESENT)
  notes     String?
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt
  class     Class            @relation(fields: [classId], references: [id])
  student   Student          @relation(fields: [studentId], references: [id])

  @@unique([studentId, classId])
  @@map("attendances")
}

model Payment {
  id            String        @id @default(cuid())
  studentId     String
  amount        Decimal
  method        PaymentMethod
  status        PaymentStatus @default(PAID)
  description   String?
  transactionId String?
  dueDate       DateTime?
  paidDate      DateTime?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  student       Student       @relation(fields: [studentId], references: [id])

  @@map("payments")
}

model ActivityLog {
  id         String   @id @default(cuid())
  userId     String
  userRole   Role
  action     String
  resource   String
  resourceId String?
  details    Json?
  ipAddress  String?
  userAgent  String?
  createdAt  DateTime @default(now())
  user       User     @relation(fields: [userId], references: [id])

  @@map("activity_logs")
}

model Assessment {
  id          String         @id @default(cuid())
  studentId   String?
  groupId     String?
  testName    String
  type        AssessmentType
  level       Level?
  score       Int?
  maxScore    Int?
  passed      Boolean        @default(false)
  questions   Json?
  results     Json?
  assignedBy  String?
  assignedAt  DateTime?
  startedAt   DateTime?
  completedAt DateTime?
  branch      String         @default("main")
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt
  group       Group?         @relation(fields: [groupId], references: [id])
  student     Student?       @relation(fields: [studentId], references: [id])

  @@map("assessments")
}

model Message {
  id            String    @id @default(cuid())
  subject       String
  content       String
  recipientType String
  recipientIds  String[]
  priority      String    @default("MEDIUM")
  status        String    @default("DRAFT")
  sentAt        DateTime?
  senderId      String
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  sender        User      @relation(fields: [senderId], references: [id])

  @@map("messages")
}

model Announcement {
  id             String   @id @default(cuid())
  title          String
  content        String
  priority       String   @default("MEDIUM")
  targetAudience String   @default("ALL")
  isActive       Boolean  @default(true)
  authorId       String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  author         User     @relation(fields: [authorId], references: [id])

  @@map("announcements")
}

enum Role {
  ADMIN
  MANAGER
  TEACHER
  RECEPTION
  CASHIER
  STUDENT
  ACADEMIC_MANAGER
}

enum Level {
  A1
  A2
  B1
  B2
  IELTS
  SAT
  MATH
  KIDS
}

enum LeadStatus {
  NEW
  CALLING
  CALL_COMPLETED
  GROUP_ASSIGNED
  ARCHIVED
  NOT_INTERESTED
}

enum StudentStatus {
  ACTIVE
  DROPPED
  PAUSED
  COMPLETED
}

enum EnrollmentStatus {
  ACTIVE
  COMPLETED
  DROPPED
  SUSPENDED
}

enum AttendanceStatus {
  PRESENT
  ABSENT
  LATE
  EXCUSED
}

enum PaymentMethod {
  CASH
  CARD
}

enum PaymentStatus {
  PAID
  DEBT
  REFUNDED
}

enum AssessmentType {
  LEVEL_TEST
  PROGRESS_TEST
  FINAL_EXAM
  GROUP_TEST
}

enum AssessmentStatus {
  DRAFT
  ASSIGNED
  IN_PROGRESS
  COMPLETED
  EXPIRED
}

enum TeacherTier {
  A_LEVEL
  B_LEVEL
  C_LEVEL
  NEW
}
