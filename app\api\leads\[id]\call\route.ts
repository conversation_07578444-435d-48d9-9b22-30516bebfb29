import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ActivityLogger } from '@/lib/activity-logger'
import { Role } from '@prisma/client'
import * as z from 'zod'

const startCallSchema = z.object({
  notes: z.string().optional(),
})

const endCallSchema = z.object({
  duration: z.number().min(0),
  notes: z.string().optional(),
  recordingUrl: z.string().optional(),
})

// Start a call
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = startCallSchema.parse(body)

    // Check if lead exists
    const lead = await prisma.lead.findUnique({
      where: { id },
    })

    if (!lead) {
      return NextResponse.json({ error: 'Lead not found' }, { status: 404 })
    }

    // Check if there's already an active call
    const activeCall = await prisma.callRecord.findFirst({
      where: {
        leadId: id,
        endedAt: null,
      },
    })

    if (activeCall) {
      return NextResponse.json({ error: 'Call already in progress' }, { status: 400 })
    }

    const now = new Date()

    // Create call record
    const callRecord = await prisma.callRecord.create({
      data: {
        leadId: id,
        userId: session.user.id,
        startedAt: now,
        notes: validatedData.notes,
      },
    })

    // Update lead status to CALLING
    const updatedLead = await prisma.lead.update({
      where: { id },
      data: {
        status: 'CALLING',
        callStartedAt: now,
        updatedAt: now,
      },
    })

    // Log activity
    await ActivityLogger.logLeadContacted(
      session.user.id,
      session.user.role as Role,
      lead.id,
      {
        leadName: lead.name,
        leadPhone: lead.phone,
        previousStatus: lead.status,
        newStatus: 'CALLING',
        notes: 'Call started',
      },
      request
    )

    return NextResponse.json({
      callRecord,
      lead: updatedLead,
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error starting call:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// End a call
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = endCallSchema.parse(body)

    // Find the active call
    const activeCall = await prisma.callRecord.findFirst({
      where: {
        leadId: id,
        userId: session.user.id,
        endedAt: null,
      },
    })

    if (!activeCall) {
      return NextResponse.json({ error: 'No active call found' }, { status: 404 })
    }

    const now = new Date()

    // Update call record
    const updatedCallRecord = await prisma.callRecord.update({
      where: { id: activeCall.id },
      data: {
        endedAt: now,
        duration: validatedData.duration,
        notes: validatedData.notes || activeCall.notes,
        recordingUrl: validatedData.recordingUrl,
        updatedAt: now,
      },
    })

    // Update lead status to CALL_COMPLETED
    const updatedLead = await prisma.lead.update({
      where: { id },
      data: {
        status: 'CALL_COMPLETED',
        callEndedAt: now,
        callDuration: validatedData.duration,
        updatedAt: now,
      },
    })

    // Log activity
    const lead = await prisma.lead.findUnique({ where: { id } })
    if (lead) {
      await ActivityLogger.logLeadContacted(
        session.user.id,
        session.user.role as Role,
        lead.id,
        {
          leadName: lead.name,
          leadPhone: lead.phone,
          previousStatus: 'CALLING',
          newStatus: 'CALL_COMPLETED',
          notes: `Call completed (${Math.floor(validatedData.duration / 60)}m ${validatedData.duration % 60}s)`,
        },
        request
      )
    }

    return NextResponse.json({
      callRecord: updatedCallRecord,
      lead: updatedLead,
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error ending call:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
