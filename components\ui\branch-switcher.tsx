'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Badge } from '@/components/ui/badge'
import { Building2, Check, ChevronDown } from 'lucide-react'
import { useBranch } from '@/contexts/branch-context'

export function BranchSwitcher() {
  const { currentBranch, branches, switchBranch, isLoading } = useBranch()
  const [isOpen, setIsOpen] = useState(false)

  if (isLoading) {
    return (
      <div className="flex items-center gap-2 px-3 py-2 bg-gray-100 rounded-md">
        <Building2 className="h-4 w-4" />
        <span className="text-sm">Loading...</span>
      </div>
    )
  }

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          className="flex items-center gap-3 min-w-[280px] justify-between bg-white/80 backdrop-blur-sm border-gray-200 hover:bg-white hover:shadow-sm transition-all duration-200 rounded-xl py-3"
        >
          <div className="flex items-center gap-3">
            <div className="h-8 w-8 rounded-lg bg-blue-50 flex items-center justify-center">
              <Building2 className="h-4 w-4 text-blue-600" />
            </div>
            <div className="flex flex-col items-start">
              <span className="font-semibold text-gray-900 text-sm">{currentBranch.name}</span>
              <Badge variant="secondary" className="text-xs font-medium bg-green-100 text-green-700">
                Active
              </Badge>
            </div>
          </div>
          <ChevronDown className="h-4 w-4 text-gray-400" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-[280px] rounded-xl border-gray-100 shadow-lg">
        <DropdownMenuLabel className="text-sm font-semibold text-gray-900 px-4 py-3">Switch Branch</DropdownMenuLabel>
        <DropdownMenuSeparator className="bg-gray-100" />
        {branches.map((branch) => (
          <DropdownMenuItem
            key={branch.id}
            onClick={() => {
              switchBranch(branch.id)
              setIsOpen(false)
            }}
            className="flex items-center justify-between cursor-pointer px-4 py-3 hover:bg-gray-50 transition-colors duration-200"
          >
            <div className="flex flex-col">
              <div className="flex items-center gap-3">
                <div className="h-6 w-6 rounded-md bg-gray-100 flex items-center justify-center">
                  <Building2 className="h-3.5 w-3.5 text-gray-600" />
                </div>
                <span className="font-medium text-gray-900">{branch.name}</span>
                {currentBranch.id === branch.id && (
                  <Check className="h-4 w-4 text-green-600" />
                )}
              </div>
              {branch.address && (
                <span className="text-xs text-gray-500 ml-9 mt-0.5">
                  {branch.address}
                </span>
              )}
            </div>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
