// Inter-Server Communication Library
// Handles secure communication between admin and staff servers

import { NextRequest } from 'next/server';

export interface InterServerRequest {
  endpoint: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  data?: any;
  headers?: Record<string, string>;
}

export interface InterServerResponse {
  success: boolean;
  data?: any;
  error?: string;
  status: number;
}

/**
 * Validates inter-server request authentication
 */
export function validateInterServerAuth(request: NextRequest): boolean {
  const authHeader = request.headers.get('X-Inter-Server-Secret');
  const timestampHeader = request.headers.get('X-Timestamp');
  const expectedSecret = process.env.INTER_SERVER_SECRET;

  if (!authHeader || !expectedSecret) {
    return false;
  }

  // Validate secret
  if (authHeader !== expectedSecret) {
    return false;
  }

  // Validate timestamp (prevent replay attacks)
  if (timestampHeader) {
    const requestTime = parseInt(timestampHeader);
    const currentTime = Date.now();
    const maxAge = 5 * 60 * 1000; // 5 minutes

    if (isNaN(requestTime) || (currentTime - requestTime) > maxAge) {
      console.warn('Inter-server request rejected: timestamp too old or invalid');
      return false;
    }
  }

  return true;
}

/**
 * Creates authenticated headers for inter-server requests
 */
export function createInterServerHeaders(): Record<string, string> {
  const secret = process.env.INTER_SERVER_SECRET;

  if (!secret) {
    throw new Error('INTER_SERVER_SECRET not configured');
  }

  const timestamp = Date.now().toString();
  const serverConfig = InterServerUtils.getServerConfig();
  const requestId = `${serverConfig.serverType}-${timestamp}-${Math.random().toString(36).substr(2, 9)}`;

  return {
    'Content-Type': 'application/json',
    'X-Inter-Server-Secret': secret,
    'X-Source-Server': serverConfig.serverType,
    'X-Request-ID': requestId,
    'X-Timestamp': timestamp,
    'User-Agent': `${serverConfig.serverType}-server`,
  };
}

/**
 * Makes authenticated request to another server
 */
export async function makeInterServerRequest(
  targetServer: 'admin' | 'staff',
  request: InterServerRequest
): Promise<InterServerResponse> {
  try {
    const baseUrl = targetServer === 'admin' 
      ? process.env.ADMIN_SERVER_URL 
      : process.env.STAFF_SERVER_URL;
    
    if (!baseUrl) {
      throw new Error(`${targetServer.toUpperCase()}_SERVER_URL not configured`);
    }
    
    const url = `${baseUrl}${request.endpoint}`;
    const headers = {
      ...createInterServerHeaders(),
      ...request.headers,
    };
    
    const response = await fetch(url, {
      method: request.method,
      headers,
      body: request.data ? JSON.stringify(request.data) : undefined,
    });
    
    const responseData = await response.json();
    
    return {
      success: response.ok,
      data: responseData,
      status: response.status,
      error: response.ok ? undefined : responseData.error || 'Request failed',
    };
  } catch (error) {
    return {
      success: false,
      status: 500,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Middleware for protecting inter-server endpoints
 */
export function withInterServerAuth(handler: Function) {
  return async (request: NextRequest, ...args: any[]) => {
    if (!validateInterServerAuth(request)) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized inter-server request' }),
        { 
          status: 401,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
    
    return handler(request, ...args);
  };
}

/**
 * Staff server functions - for requesting data from admin server
 */
export class StaffServerAPI {
  /**
   * Request user authentication from admin server
   */
  static async authenticateUser(phone: string, password: string): Promise<InterServerResponse> {
    return makeInterServerRequest('admin', {
      endpoint: '/api/inter-server/auth/validate',
      method: 'POST',
      data: { phone, password },
    });
  }
  
  /**
   * Get user data from admin server
   */
  static async getUserData(userId: string): Promise<InterServerResponse> {
    return makeInterServerRequest('admin', {
      endpoint: `/api/inter-server/users/${userId}`,
      method: 'GET',
    });
  }
  
  /**
   * Sync data with admin server
   */
  static async syncData(dataType: string, data: any): Promise<InterServerResponse> {
    return makeInterServerRequest('admin', {
      endpoint: '/api/inter-server/sync',
      method: 'POST',
      data: { type: dataType, data },
    });
  }
}

/**
 * Admin server functions - for handling requests from staff server
 */
export class AdminServerAPI {
  /**
   * Validate staff server request
   */
  static async validateStaffRequest(request: NextRequest): Promise<boolean> {
    return validateInterServerAuth(request);
  }
  
  /**
   * Send data to staff server
   */
  static async sendToStaff(endpoint: string, data: any): Promise<InterServerResponse> {
    return makeInterServerRequest('staff', {
      endpoint,
      method: 'POST',
      data,
    });
  }
  
  /**
   * Broadcast update to staff server
   */
  static async broadcastUpdate(updateType: string, data: any): Promise<InterServerResponse> {
    return makeInterServerRequest('staff', {
      endpoint: '/api/inter-server/updates',
      method: 'POST',
      data: { type: updateType, data },
    });
  }
}

/**
 * Common utilities for both servers
 */
export class InterServerUtils {
  /**
   * Log inter-server communication
   */
  static logRequest(
    direction: 'incoming' | 'outgoing',
    endpoint: string,
    success: boolean,
    details?: any
  ) {
    const timestamp = new Date().toISOString();
    const serverType = process.env.SERVER_TYPE || 'unknown';
    
    console.log(`[${timestamp}] Inter-Server ${direction.toUpperCase()}: ${endpoint}`, {
      server: serverType,
      success,
      details,
    });
  }
  
  /**
   * Check if current server can communicate with target server
   */
  static async healthCheck(targetServer: 'admin' | 'staff'): Promise<boolean> {
    try {
      const response = await makeInterServerRequest(targetServer, {
        endpoint: '/api/health',
        method: 'GET',
      });
      
      return response.success;
    } catch (error) {
      return false;
    }
  }
  
  /**
   * Get server configuration
   */
  static getServerConfig(): {
    serverType: string;
    adminUrl: string;
    staffUrl: string;
    hasInterServerSecret: boolean;
  } {
    return {
      serverType: (process.env.SERVER_TYPE || 'admin') as string,
      adminUrl: (process.env.ADMIN_SERVER_URL || 'http://localhost:3001') as string,
      staffUrl: (process.env.STAFF_SERVER_URL || 'http://localhost:3000') as string,
      hasInterServerSecret: !!process.env.INTER_SERVER_SECRET,
    };
  }
}
