import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { getToken } from 'next-auth/jwt'

// Define protected routes and their required roles (Staff Server)
const protectedRoutes = {
  '/dashboard': ['MANAGER', 'TEACHER', 'RECEPTION', 'ACADEMIC_MANAGER'],
  '/dashboard/teachers': ['MANAGER'],
  '/dashboard/students': ['MANAGER', 'TEACHER', 'RECEPTION'],
  '/dashboard/groups': ['MANAGER', 'TEACHER'],
  '/dashboard/enrollments': ['MANAGER', 'RECEPTION'],
  '/dashboard/attendance': ['MANAGER', 'TEACHER'],
  '/dashboard/assessments': ['MANAGER', 'TEACHER', 'ACADEMIC_MANAGER'],
  '/dashboard/classes': ['MANAGER', 'TEACHER'],
  '/dashboard/leads': ['MANAGER', 'RECEPTION'],
  '/dashboard/communication': ['MANAGER', 'TEACHER', 'RECEPTION'],
}

// API routes that require authentication (Staff Server)
const protectedApiRoutes = {
  '/api/teachers': ['MANAGER'],
  '/api/students': ['MANAGER', 'TEACHER', 'RECEPTION', 'ACADEMIC_MANAGER'],
  '/api/groups': ['MANAGER', 'TEACHER'],
  '/api/enrollments': ['MANAGER', 'RECEPTION'],
  '/api/attendance': ['MANAGER', 'TEACHER'],
  '/api/assessments': ['MANAGER', 'TEACHER', 'ACADEMIC_MANAGER'],
  '/api/leads': ['MANAGER', 'RECEPTION'],
  '/api/courses': ['MANAGER'],
}

// Public routes that don't require authentication
const publicRoutes = [
  '/',
  '/auth/signin',
  '/auth/signup',
  '/auth/error',
  '/api/auth',
  '/api/health', // Allow health check endpoint
  '/api/leads', // Allow public lead submission
  '/api/auth/verify', // Allow inter-server authentication verification
]

// Specific inter-server routes (more restrictive than wildcard)
const interServerRoutes = [
  '/api/inter-server/health',
  '/api/inter-server/auth/validate',
  '/api/inter-server/users',
]

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Skip middleware for static files and Next.js internals
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/static') ||
    pathname.includes('.') ||
    pathname.startsWith('/favicon')
  ) {
    return NextResponse.next()
  }

  // Check if route is public
  const isPublicRoute = publicRoutes.some(route => {
    if (route === pathname) return true
    if (route.endsWith('*') && pathname.startsWith(route.slice(0, -1))) return true
    return false
  })

  // Check if route is an allowed inter-server route
  const isInterServerRoute = interServerRoutes.includes(pathname)

  // Allow public routes and specific inter-server routes
  if (isPublicRoute || isInterServerRoute) {
    return NextResponse.next()
  }

  // Get the token from the request
  const token = await getToken({
    req: request,
    secret: process.env.NEXTAUTH_SECRET,
  })

  // Redirect to signin if no token
  if (!token) {
    const signInUrl = new URL('/auth/signin', request.url)
    signInUrl.searchParams.set('callbackUrl', pathname)
    return NextResponse.redirect(signInUrl)
  }

  // Check role-based access for protected routes
  const userRole = token.role as string

  // Staff server: Only allow staff roles (RECEPTION, ACADEMIC_MANAGER, TEACHER, MANAGER)
  const serverType = process.env.SERVER_TYPE || 'staff'
  if (serverType === 'staff') {
    const allowedRoles = ['RECEPTION', 'ACADEMIC_MANAGER', 'TEACHER', 'MANAGER']
    if (!allowedRoles.includes(userRole)) {
      return NextResponse.redirect(new URL('/auth/signin?error=unauthorized', request.url))
    }
  }

  // Check dashboard routes
  for (const [route, allowedRoles] of Object.entries(protectedRoutes)) {
    if (pathname.startsWith(route)) {
      if (!allowedRoles.includes(userRole)) {
        return NextResponse.redirect(new URL('/dashboard/unauthorized', request.url))
      }
      break
    }
  }

  // Check API routes
  for (const [route, allowedRoles] of Object.entries(protectedApiRoutes)) {
    if (pathname.startsWith(route)) {
      if (!allowedRoles.includes(userRole)) {
        return NextResponse.json(
          { error: 'Unauthorized access' },
          { status: 403 }
        )
      }
      break
    }
  }

  // Special handling for student access
  if (userRole === 'STUDENT') {
    // Students can only access their own data
    const userId = token.sub

    // Allow access to student dashboard
    if (pathname.startsWith('/dashboard/student')) {
      return NextResponse.next()
    }

    // Restrict access to other dashboard routes
    if (pathname.startsWith('/dashboard') && pathname !== '/dashboard') {
      return NextResponse.redirect(new URL('/dashboard/student', request.url))
    }
  }

  // Special handling for academic manager access
  if (userRole === 'ACADEMIC_MANAGER') {
    // Academic managers have access to assessments and test statistics
    const allowedPaths = ['/dashboard', '/dashboard/assessments', '/dashboard/students']
    const isAllowed = allowedPaths.some(path => pathname.startsWith(path))

    if (!isAllowed && pathname.startsWith('/dashboard')) {
      return NextResponse.redirect(new URL('/dashboard/assessments', request.url))
    }
  }

  // Teacher-specific restrictions
  if (userRole === 'TEACHER') {
    // Teachers can access their assigned groups and students
    if (pathname.startsWith('/dashboard/teacher')) {
      return NextResponse.next()
    }
  }

  // Reception-specific restrictions
  if (userRole === 'RECEPTION') {
    // Reception can access leads, students, and enrollments
    const allowedPaths = ['/dashboard', '/dashboard/leads', '/dashboard/students', '/dashboard/enrollments']
    if (!allowedPaths.some(path => pathname.startsWith(path))) {
      return NextResponse.redirect(new URL('/dashboard', request.url))
    }
  }



  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api/auth (NextAuth.js routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!api/auth|_next/static|_next/image|favicon.ico|public).*)',
  ],
}
