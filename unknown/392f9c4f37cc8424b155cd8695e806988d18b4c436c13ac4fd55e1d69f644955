'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { formatDate } from '@/lib/utils'
import { 
  User, 
  Phone, 
  Mail, 
  MapPin, 
  Calendar, 
  GraduationCap, 
  BookOpen,
  Users,
  DollarSign,
  Clock,
  Edit,
  ArrowLeft,
  Award
} from 'lucide-react'
import Link from 'next/link'

interface TeacherDetail {
  id: string
  userId: string
  subject: string
  experience: number | null
  branch: string
  photoUrl: string | null
  createdAt: string
  user: {
    id: string
    name: string
    phone: string
    email: string | null
    role: string
    createdAt: string
  }
  groups: Array<{
    id: string
    name: string
    capacity: number
    startDate: string
    endDate: string
    isActive: boolean
    course: {
      id: string
      name: string
      level: string
      duration: number
      price: number
    }
    enrollments: Array<{
      id: string
      status: string
      student: {
        user: {
          name: string
        }
      }
    }>
    _count: {
      enrollments: number
      classes: number
    }
  }>
  classes: Array<{
    id: string
    date: string
    topic: string | null
    homework: string | null
    notes: string | null
    group: {
      id: string
      name: string
    }
    _count: {
      attendances: number
    }
  }>
  _count: {
    groups: number
    classes: number
  }
  totalStudents?: number
}

export default function TeacherDetailPage() {
  const params = useParams()
  const [teacher, setTeacher] = useState<TeacherDetail | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (params.id) {
      fetchTeacher(params.id as string)
    }
  }, [params.id])

  const fetchTeacher = async (id: string) => {
    try {
      const response = await fetch(`/api/teachers/${id}`)
      const data = await response.json()
      setTeacher(data)
    } catch (error) {
      console.error('Error fetching teacher:', error)
    } finally {
      setLoading(false)
    }
  }

  const getExperienceColor = (experience: number | null) => {
    if (!experience) return 'bg-gray-100 text-gray-800'
    if (experience < 2) return 'bg-yellow-100 text-yellow-800'
    if (experience < 5) return 'bg-blue-100 text-blue-800'
    return 'bg-green-100 text-green-800'
  }

  const getExperienceLabel = (experience: number | null) => {
    if (!experience) return 'New'
    if (experience < 2) return 'Junior'
    if (experience < 5) return 'Mid-level'
    return 'Senior'
  }



  if (loading) {
    return <div className="flex justify-center items-center h-64">Loading...</div>
  }

  if (!teacher) {
    return <div className="flex justify-center items-center h-64">Teacher not found</div>
  }

  // Calculate statistics
  const activeGroups = teacher.groups.filter(g => g.isActive).length
  const totalStudents = teacher.groups.reduce((sum, group) => sum + group._count.enrollments, 0)
  const totalClasses = teacher._count.classes
  const averageClassSize = activeGroups > 0 ? totalStudents / activeGroups : 0

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/dashboard/teachers">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Teachers
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{teacher.user.name}</h1>
            <p className="text-gray-600">Teacher Profile</p>
          </div>
        </div>
        <Button>
          <Edit className="h-4 w-4 mr-2" />
          Edit Profile
        </Button>
      </div>

      {/* Teacher Info Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Personal Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="h-5 w-5 mr-2" />
              Personal Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-3">
              <Phone className="h-4 w-4 text-gray-400" />
              <span>{teacher.user.phone}</span>
            </div>
            {teacher.user.email && (
              <div className="flex items-center space-x-3">
                <Mail className="h-4 w-4 text-gray-400" />
                <span>{teacher.user.email}</span>
              </div>
            )}
            <div className="flex items-center space-x-3">
              <MapPin className="h-4 w-4 text-gray-400" />
              <span>{teacher.branch}</span>
            </div>
            <div className="flex items-center space-x-3">
              <Calendar className="h-4 w-4 text-gray-400" />
              <span>Joined {formatDate(teacher.user.createdAt)}</span>
            </div>
            <div className="pt-2 border-t">
              <div className="flex items-center space-x-2 mb-2">
                <Award className="h-4 w-4 text-gray-400" />
                <span className="text-sm text-gray-600">Role: {teacher.user.role}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Professional Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BookOpen className="h-5 w-5 mr-2" />
              Professional Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Subject</span>
              <span className="font-semibold">{teacher.subject}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Experience</span>
              <Badge className={getExperienceColor(teacher.experience)}>
                {teacher.experience ? `${teacher.experience}y` : '0y'} - {getExperienceLabel(teacher.experience)}
              </Badge>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Branch</span>
              <span className="font-semibold">{teacher.branch}</span>
            </div>
          </CardContent>
        </Card>

        {/* Teaching Statistics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <GraduationCap className="h-5 w-5 mr-2" />
              Teaching Overview
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Active Groups</span>
              <span className="font-semibold">{activeGroups}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Total Students</span>
              <span className="font-semibold">{totalStudents}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Total Classes</span>
              <span className="font-semibold">{totalClasses}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Avg. Class Size</span>
              <span className="font-semibold">{averageClassSize.toFixed(1)}</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <GraduationCap className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Groups</p>
                <p className="text-2xl font-bold text-gray-900">{activeGroups}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Students</p>
                <p className="text-2xl font-bold text-gray-900">{totalStudents}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Classes Taught</p>
                <p className="text-2xl font-bold text-gray-900">{totalClasses}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Award className="h-8 w-8 text-yellow-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Experience</p>
                <p className="text-2xl font-bold text-gray-900">{teacher.experience || 0}y</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Assigned Groups */}
      <Card>
        <CardHeader>
          <CardTitle>Assigned Groups ({teacher.groups.length})</CardTitle>
          <CardDescription>Groups currently assigned to this teacher</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Group</TableHead>
                <TableHead>Course</TableHead>
                <TableHead>Level</TableHead>
                <TableHead>Students</TableHead>
                <TableHead>Duration</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {teacher.groups.map((group) => (
                <TableRow key={group.id}>
                  <TableCell>
                    <div className="font-medium">{group.name}</div>
                  </TableCell>
                  <TableCell>{group.course.name}</TableCell>
                  <TableCell>
                    <Badge variant="outline">{group.course.level}</Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Users className="h-4 w-4 mr-1 text-blue-600" />
                      <span>{group._count.enrollments}/{group.capacity}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {formatDate(group.startDate)} - {formatDate(group.endDate)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge className={group.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                      {group.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm">
                        View
                      </Button>
                      <Button variant="outline" size="sm">
                        Manage
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Recent Classes */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Classes</CardTitle>
          <CardDescription>Latest class sessions taught by this teacher</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date</TableHead>
                <TableHead>Group</TableHead>
                <TableHead>Topic</TableHead>
                <TableHead>Attendance</TableHead>
                <TableHead>Homework</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {teacher.classes.slice(0, 10).map((classItem) => (
                <TableRow key={classItem.id}>
                  <TableCell>{formatDate(classItem.date)}</TableCell>
                  <TableCell>{classItem.group.name}</TableCell>
                  <TableCell>{classItem.topic || 'No topic set'}</TableCell>
                  <TableCell>
                    <span className="text-sm">
                      {classItem._count.attendances} students
                    </span>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm text-gray-600">
                      {classItem.homework ? 'Assigned' : 'None'}
                    </span>
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm">
                        View
                      </Button>
                      <Button variant="outline" size="sm">
                        Edit
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
