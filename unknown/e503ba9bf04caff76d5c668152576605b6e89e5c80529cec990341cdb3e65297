import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    // Check if user has permission to view this student's certificates
    if (session.user.role === 'STUDENT' && session.user.id !== id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Get student information with enrollments
    const student = await prisma.student.findUnique({
      where: { userId: id },
      include: {
        user: {
          select: {
            name: true,
            email: true,
          },
        },
        enrollments: {
          include: {
            group: {
              include: {
                course: {
                  select: {
                    name: true,
                    level: true,
                    duration: true,
                  },
                },
              },
            },
          },
          orderBy: { createdAt: 'desc' },
        },
        currentGroup: {
          include: {
            course: {
              select: {
                name: true,
                level: true,
                duration: true,
              },
            },
          },
        },
        assessments: {
          where: {
            type: 'FINAL_EXAM',
            passed: true,
          },
          include: {
            group: {
              include: {
                course: {
                  select: {
                    name: true,
                    level: true,
                  },
                },
              },
            },
          },
          orderBy: { completedAt: 'desc' },
        },
      },
    })

    if (!student) {
      return NextResponse.json({ error: 'Student not found' }, { status: 404 })
    }

    // Generate certificates based on completed enrollments and passed final exams
    const completed = []
    const inProgress = []
    const upcoming = []

    // Process completed enrollments with passed final exams
    for (const assessment of student.assessments) {
      if (assessment.group && assessment.completedAt) {
        const certificateNumber = `INN-${assessment.group.course.level}-${new Date(assessment.completedAt).getFullYear()}-${String(Math.floor(Math.random() * 1000)).padStart(3, '0')}`
        
        completed.push({
          id: assessment.id,
          name: `${assessment.group.course.name} Certificate`,
          course: assessment.group.course.name,
          completionDate: assessment.completedAt,
          grade: assessment.score && assessment.maxScore 
            ? assessment.score >= assessment.maxScore * 0.9 ? 'Excellent'
            : assessment.score >= assessment.maxScore * 0.8 ? 'Very Good'
            : assessment.score >= assessment.maxScore * 0.7 ? 'Good'
            : 'Satisfactory'
            : 'Pass',
          score: assessment.score || 0,
          issueDate: new Date(new Date(assessment.completedAt).getTime() + 5 * 24 * 60 * 60 * 1000).toISOString(), // 5 days after completion
          certificateNumber,
          status: 'issued',
        })
      }
    }

    // Process current enrollment as in progress
    if (student.currentGroup) {
      // Calculate progress based on course duration and start date
      const courseStartDate = new Date(student.currentGroup.startDate)
      const courseDurationMs = student.currentGroup.course.duration * 7 * 24 * 60 * 60 * 1000
      const timeElapsed = Date.now() - courseStartDate.getTime()
      const enrollmentProgress = Math.min(95, Math.max(5, Math.floor((timeElapsed / courseDurationMs) * 100)))

      inProgress.push({
        id: student.currentGroup.id,
        name: `${student.currentGroup.course.name} Certificate`,
        course: student.currentGroup.course.name,
        expectedCompletion: new Date(courseStartDate.getTime() + courseDurationMs).toISOString(),
        progress: enrollmentProgress,
        status: 'in_progress',
      })
    }

    // Generate upcoming certificates based on level progression
    const levelOrder = ['A1', 'A2', 'B1', 'B2', 'IELTS']
    const currentLevelIndex = levelOrder.indexOf(student.level)
    
    if (currentLevelIndex >= 0 && currentLevelIndex < levelOrder.length - 1) {
      const nextLevel = levelOrder[currentLevelIndex + 1]
      upcoming.push({
        id: `upcoming-${nextLevel}`,
        name: `General English ${nextLevel} Certificate`,
        course: `General English ${nextLevel}`,
        prerequisite: `Complete ${student.level} level`,
        estimatedDuration: '12 weeks',
        status: 'upcoming',
      })
    }

    // Add IELTS if not already completed and student is at B2 or higher
    if (!completed.some(c => c.course.includes('IELTS')) && 
        ['B2', 'IELTS'].includes(student.level)) {
      upcoming.push({
        id: 'upcoming-ielts',
        name: 'IELTS Preparation Certificate',
        course: 'IELTS Preparation',
        prerequisite: 'B2 level completion',
        estimatedDuration: '8 weeks',
        status: 'upcoming',
      })
    }

    const certificateData = {
      student: {
        name: student.user.name,
        email: student.user.email,
        level: student.level,
        branch: student.branch,
      },
      completed,
      inProgress,
      upcoming,
      statistics: {
        totalCompleted: completed.length,
        inProgress: inProgress.length,
        averageGrade: completed.length > 0 
          ? completed.reduce((sum, cert) => {
              const gradePoints = cert.grade === 'Excellent' ? 4 
                : cert.grade === 'Very Good' ? 3.5
                : cert.grade === 'Good' ? 3
                : 2.5
              return sum + gradePoints
            }, 0) / completed.length
          : 0,
      },
    }

    return NextResponse.json(certificateData)
  } catch (error) {
    console.error('Error fetching student certificates:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
