import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ActivityLogger } from '@/lib/activity-logger'
import { Role } from '@prisma/client'
import * as z from 'zod'

const contactUpdateSchema = z.object({
  reEnrollmentNotes: z.string().optional(),
  lastContactedAt: z.string().optional(),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const search = searchParams.get('search')
    const branchId = searchParams.get('branch')

    // Note: Using droppedAt field instead of status since status column may not exist
    const where: any = {
      droppedAt: {
        not: null, // Students with droppedAt are considered dropped
      },
    }

    if (branchId) {
      // Map branch ID to branch name for database query
      const branchName = branchId === 'main' ? 'Main Branch' : 'Branch'
      where.branch = branchName
    }

    if (search) {
      where.OR = [
        { user: { name: { contains: search, mode: 'insensitive' } } },
        { user: { phone: { contains: search } } },
        { user: { email: { contains: search, mode: 'insensitive' } } },
      ]
    }

    if (branchId) {
      where.branch = branchId
    }

    const [droppedStudents, total] = await Promise.all([
      prisma.student.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              phone: true,
              email: true,
            },
          },
          enrollments: {
            include: {
              group: {
                include: {
                  course: {
                    select: {
                      name: true,
                      level: true,
                    },
                  },
                },
              },
            },
            orderBy: { createdAt: 'desc' },
            take: 1, // Get last enrollment
          },
        },
        orderBy: { droppedAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.student.count({ where }),
    ])

    return NextResponse.json({
      droppedStudents,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching dropped students:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Only allow certain roles to contact dropped students
    if (!session.user.role || !['ADMIN', 'MANAGER', 'RECEPTION'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const studentId = searchParams.get('studentId')

    if (!studentId) {
      return NextResponse.json(
        { error: 'Student ID is required' },
        { status: 400 }
      )
    }

    const body = await request.json()
    const validatedData = contactUpdateSchema.parse(body)

    // Check if student exists and is dropped
    const existingStudent = await prisma.student.findFirst({
      where: {
        id: studentId,
        droppedAt: {
          not: null, // Student is dropped if droppedAt is set
        },
      },
      include: {
        user: {
          select: {
            name: true,
          },
        },
      },
    })

    if (!existingStudent) {
      return NextResponse.json(
        { error: 'Dropped student not found' },
        { status: 404 }
      )
    }

    // Update contact information
    const updateData: any = {
      updatedAt: new Date(),
    }

    if (validatedData.reEnrollmentNotes) {
      updateData.reEnrollmentNotes = validatedData.reEnrollmentNotes
    }

    if (validatedData.lastContactedAt) {
      updateData.lastContactedAt = new Date(validatedData.lastContactedAt)
    } else {
      updateData.lastContactedAt = new Date() // Auto-set to now if not provided
    }

    const updatedStudent = await prisma.student.update({
      where: { id: studentId },
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            phone: true,
            email: true,
          },
        },
      },
    })

    // Log the activity
    await ActivityLogger.logDroppedStudentContacted(
      session.user.id,
      session.user.role as Role,
      studentId,
      {
        studentName: existingStudent.user.name,
        notes: validatedData.reEnrollmentNotes,
      },
      request
    )

    return NextResponse.json(updatedStudent)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating dropped student contact:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
