import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  CreditCard, 
  Download, 
  Calendar,
  CheckCircle,
  AlertCircle,
  Clock,
  DollarSign
} from 'lucide-react'

export default function StudentPaymentsPage() {
  // Mock data - in real implementation, this would come from API
  const paymentData = {
    summary: {
      totalAmount: 2400000,
      paidAmount: 1800000,
      pendingAmount: 600000,
      nextDueDate: "2024-02-01"
    },
    payments: [
      {
        id: 1,
        date: "2024-01-15",
        amount: 600000,
        course: "General English B1",
        method: "Bank Transfer",
        status: "paid",
        receiptNumber: "RCP-2024-001"
      },
      {
        id: 2,
        date: "2024-01-01",
        amount: 600000,
        course: "General English B1",
        method: "Cash",
        status: "paid",
        receiptNumber: "RCP-2024-002"
      },
      {
        id: 3,
        date: "2023-12-15",
        amount: 600000,
        course: "General English B1",
        method: "Bank Transfer",
        status: "paid",
        receiptNumber: "RCP-2023-045"
      },
      {
        id: 4,
        date: "2024-02-01",
        amount: 600000,
        course: "General English B1",
        method: "Pending",
        status: "pending",
        receiptNumber: null
      }
    ],
    paymentPlan: {
      totalCourse: 2400000,
      installments: 4,
      installmentAmount: 600000,
      paidInstallments: 3,
      remainingInstallments: 1
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-orange-100 text-orange-800'
      case 'overdue':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid':
        return <CheckCircle className="h-4 w-4" />
      case 'pending':
        return <Clock className="h-4 w-4" />
      case 'overdue':
        return <AlertCircle className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  const formatCurrency = (amount: number) => {
    return amount.toLocaleString() + ' UZS'
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">My Payments</h1>
        <p className="text-gray-600">Track your payment history and upcoming dues</p>
      </div>

      {/* Payment Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Amount</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(paymentData.summary.totalAmount)}</div>
            <p className="text-xs text-muted-foreground">Course fee</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Paid Amount</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{formatCurrency(paymentData.summary.paidAmount)}</div>
            <p className="text-xs text-muted-foreground">
              {Math.round((paymentData.summary.paidAmount / paymentData.summary.totalAmount) * 100)}% completed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Amount</CardTitle>
            <AlertCircle className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{formatCurrency(paymentData.summary.pendingAmount)}</div>
            <p className="text-xs text-muted-foreground">Remaining balance</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Next Due Date</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{paymentData.summary.nextDueDate}</div>
            <p className="text-xs text-muted-foreground">Upcoming payment</p>
          </CardContent>
        </Card>
      </div>

      {/* Payment Progress */}
      <Card>
        <CardHeader>
          <CardTitle>Payment Progress</CardTitle>
          <CardDescription>Your payment plan progress</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Installments Paid</span>
              <span className="font-medium">
                {paymentData.paymentPlan.paidInstallments} of {paymentData.paymentPlan.installments}
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div 
                className="bg-green-500 h-3 rounded-full" 
                style={{ 
                  width: `${(paymentData.paymentPlan.paidInstallments / paymentData.paymentPlan.installments) * 100}%` 
                }}
              ></div>
            </div>
            <div className="flex justify-between text-sm text-gray-600">
              <span>Installment Amount: {formatCurrency(paymentData.paymentPlan.installmentAmount)}</span>
              <span>Remaining: {paymentData.paymentPlan.remainingInstallments} payments</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Payment History */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CreditCard className="h-5 w-5 mr-2" />
            Payment History
          </CardTitle>
          <CardDescription>All your payment transactions</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {paymentData.payments.map((payment) => (
              <div key={payment.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(payment.status)}
                    <div>
                      <h4 className="font-medium">{payment.course}</h4>
                      <p className="text-sm text-gray-600">{payment.date}</p>
                      {payment.receiptNumber && (
                        <p className="text-xs text-gray-500">Receipt: {payment.receiptNumber}</p>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <div className="font-medium">{formatCurrency(payment.amount)}</div>
                    <div className="text-sm text-gray-500">{payment.method}</div>
                  </div>
                  <Badge className={getStatusColor(payment.status)}>
                    {payment.status}
                  </Badge>
                  {payment.status === 'paid' && (
                    <Button size="sm" variant="outline">
                      <Download className="h-4 w-4 mr-1" />
                      Receipt
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Payment Methods */}
      <Card>
        <CardHeader>
          <CardTitle>Payment Methods</CardTitle>
          <CardDescription>Available payment options</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 border rounded-lg text-center">
              <CreditCard className="h-8 w-8 mx-auto mb-2 text-blue-600" />
              <h4 className="font-medium">Bank Transfer</h4>
              <p className="text-sm text-gray-600">Direct bank transfer</p>
            </div>
            <div className="p-4 border rounded-lg text-center">
              <DollarSign className="h-8 w-8 mx-auto mb-2 text-green-600" />
              <h4 className="font-medium">Cash Payment</h4>
              <p className="text-sm text-gray-600">Pay at reception</p>
            </div>
            <div className="p-4 border rounded-lg text-center">
              <CreditCard className="h-8 w-8 mx-auto mb-2 text-purple-600" />
              <h4 className="font-medium">Online Payment</h4>
              <p className="text-sm text-gray-600">Credit/Debit card</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4">
            <Button>
              <CreditCard className="h-4 w-4 mr-2" />
              Make Payment
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Download All Receipts
            </Button>
            <Button variant="outline">
              <Calendar className="h-4 w-4 mr-2" />
              View Payment Schedule
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
