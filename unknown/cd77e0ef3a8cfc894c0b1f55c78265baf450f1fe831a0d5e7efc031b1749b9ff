import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ActivityLogger } from '@/lib/activity-logger'
import { Role } from '@prisma/client'
import * as z from 'zod'

const announcementSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  content: z.string().min(1, 'Content is required'),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).default('MEDIUM'),
  targetAudience: z.enum(['STUDENTS', 'TEACHERS', 'ACADEMIC_MANAGERS', 'ALL']).default('ALL'),
  isActive: z.boolean().default(true),
  expiresAt: z.string().optional(),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const priority = searchParams.get('priority')
    const targetAudience = searchParams.get('targetAudience')

    const skip = (page - 1) * limit

    // Build where clause for filtering
    const whereClause: any = {}

    if (priority && priority !== 'ALL') {
      whereClause.priority = priority
    }

    if (targetAudience && targetAudience !== 'ALL') {
      whereClause.targetAudience = targetAudience
    }

    const [announcements, total] = await Promise.all([
      prisma.announcement.findMany({
        where: whereClause,
        include: {
          author: {
            select: {
              name: true,
              email: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      }),
      prisma.announcement.count({ where: whereClause }),
    ])

    return NextResponse.json({
      announcements,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching announcements:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has permission to create announcements
    if (!session.user.role || !['ADMIN', 'MANAGER'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const body = await request.json()
    const validatedData = announcementSchema.parse(body)

    // Create the announcement in the database
    const announcement = await prisma.announcement.create({
      data: {
        title: validatedData.title,
        content: validatedData.content,
        priority: validatedData.priority,
        targetAudience: validatedData.targetAudience,
        isActive: validatedData.isActive,
        authorId: session.user.id,
      },
      include: {
        author: {
          select: {
            name: true,
            email: true,
          },
        },
      },
    })

    // Log the activity
    await ActivityLogger.log({
      userId: session.user.id,
      userRole: (session.user.role as Role) || Role.ADMIN,
      action: 'CREATE',
      resource: 'ANNOUNCEMENT',
      resourceId: announcement.id,
      details: `Created announcement: ${validatedData.title}`,
    })

    return NextResponse.json(announcement, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating announcement:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
