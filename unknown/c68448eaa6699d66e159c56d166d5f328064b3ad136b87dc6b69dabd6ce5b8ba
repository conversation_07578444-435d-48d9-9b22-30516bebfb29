import { <PERSON>, CardContent, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Calendar, 
  Clock, 
  MapPin, 
  User,
  BookOpen,
  ChevronLeft,
  ChevronRight
} from 'lucide-react'

export default function StudentSchedulePage() {
  // Mock data - in real implementation, this would come from API
  const scheduleData = {
    currentWeek: "January 15-21, 2024",
    classes: [
      {
        id: 1,
        day: "Monday",
        date: "2024-01-15",
        time: "10:00 - 11:30",
        course: "General English B1",
        teacher: "<PERSON>",
        room: "Room 205",
        topic: "Present Perfect Tense",
        status: "completed"
      },
      {
        id: 2,
        day: "Wednesday",
        date: "2024-01-17",
        time: "10:00 - 11:30",
        course: "General English B1",
        teacher: "<PERSON>",
        room: "Room 205",
        topic: "Vocabulary: Travel & Tourism",
        status: "upcoming"
      },
      {
        id: 3,
        day: "Friday",
        date: "2024-01-19",
        time: "10:00 - 11:30",
        course: "General English B1",
        teacher: "<PERSON>",
        room: "Room 205",
        topic: "Reading Comprehension",
        status: "upcoming"
      },
      {
        id: 4,
        day: "Monday",
        date: "2024-01-22",
        time: "10:00 - 11:30",
        course: "General English B1",
        teacher: "Sarah <PERSON>",
        room: "Room 205",
        topic: "Speaking Practice: Describing Places",
        status: "scheduled"
      }
    ],
    upcomingAssignments: [
      {
        title: "Essay: My Dream Vacation",
        dueDate: "2024-01-20",
        course: "General English B1",
        status: "pending"
      },
      {
        title: "Vocabulary Quiz: Unit 6",
        dueDate: "2024-01-24",
        course: "General English B1",
        status: "pending"
      }
    ]
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'upcoming':
        return 'bg-blue-100 text-blue-800'
      case 'scheduled':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return '✓'
      case 'upcoming':
        return '⏰'
      case 'scheduled':
        return '📅'
      default:
        return '📅'
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">My Schedule</h1>
          <p className="text-gray-600">View your upcoming classes and assignments</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <span className="text-sm font-medium px-4">{scheduleData.currentWeek}</span>
          <Button variant="outline" size="sm">
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Weekly Schedule */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="h-5 w-5 mr-2" />
            Weekly Schedule
          </CardTitle>
          <CardDescription>Your classes for this week</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {scheduleData.classes.map((classItem) => (
              <div key={classItem.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex items-center space-x-4">
                  <div className="text-center min-w-[80px]">
                    <div className="text-sm font-medium">{classItem.day}</div>
                    <div className="text-xs text-gray-500">{classItem.date}</div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-gray-400" />
                    <span className="text-sm font-medium">{classItem.time}</span>
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium">{classItem.course}</h4>
                    <p className="text-sm text-gray-600">{classItem.topic}</p>
                    <div className="flex items-center space-x-4 mt-1">
                      <div className="flex items-center space-x-1">
                        <User className="h-3 w-3 text-gray-400" />
                        <span className="text-xs text-gray-500">{classItem.teacher}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <MapPin className="h-3 w-3 text-gray-400" />
                        <span className="text-xs text-gray-500">{classItem.room}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-lg">{getStatusIcon(classItem.status)}</span>
                  <Badge className={getStatusColor(classItem.status)}>
                    {classItem.status}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Upcoming Assignments */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <BookOpen className="h-5 w-5 mr-2" />
            Upcoming Assignments
          </CardTitle>
          <CardDescription>Assignments due soon</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {scheduleData.upcomingAssignments.map((assignment, index) => (
              <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h4 className="font-medium">{assignment.title}</h4>
                  <p className="text-sm text-gray-600">{assignment.course}</p>
                  <div className="flex items-center space-x-1 mt-1">
                    <Clock className="h-3 w-3 text-gray-400" />
                    <span className="text-xs text-gray-500">Due: {assignment.dueDate}</span>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge className="bg-orange-100 text-orange-800">
                    {assignment.status}
                  </Badge>
                  <Button size="sm" variant="outline">
                    View Details
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button variant="outline" className="h-16 flex flex-col space-y-1">
              <Calendar className="h-5 w-5" />
              <span className="text-sm">Add to Calendar</span>
            </Button>
            <Button variant="outline" className="h-16 flex flex-col space-y-1">
              <BookOpen className="h-5 w-5" />
              <span className="text-sm">Course Materials</span>
            </Button>
            <Button variant="outline" className="h-16 flex flex-col space-y-1">
              <User className="h-5 w-5" />
              <span className="text-sm">Contact Teacher</span>
            </Button>
            <Button variant="outline" className="h-16 flex flex-col space-y-1">
              <Clock className="h-5 w-5" />
              <span className="text-sm">Request Makeup</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
