import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import * as z from 'zod'

const updateCourseSchema = z.object({
  name: z.string().min(1).optional(),
  level: z.enum(['A1', 'A2', 'B1', 'B2', 'IELTS', 'SAT', 'MATH', 'KIDS']).optional(),
  description: z.string().optional(),
  duration: z.number().min(1).optional(),
  price: z.number().min(0).optional(),
  isActive: z.boolean().optional(),
})

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const course = await prisma.course.findUnique({
      where: { id },
      include: {
        groups: {
          include: {
            teacher: {
              include: {
                user: {
                  select: {
                    name: true,
                  },
                },
              },
            },
            _count: {
              select: {
                enrollments: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
        },
        _count: {
          select: {
            groups: true,
          },
        },
      },
    })

    if (!course) {
      return NextResponse.json(
        { error: 'Course not found' },
        { status: 404 }
      )
    }

    // Calculate total enrolled students across all groups
    const totalEnrolledStudents = course.groups.reduce(
      (total, group) => total + group._count.enrollments,
      0
    )

    return NextResponse.json({
      ...course,
      totalEnrolledStudents,
    })
  } catch (error) {
    console.error('Error fetching course:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()
    const validatedData = updateCourseSchema.parse(body)

    // Check if course exists
    const existingCourse = await prisma.course.findUnique({
      where: { id },
    })

    if (!existingCourse) {
      return NextResponse.json(
        { error: 'Course not found' },
        { status: 404 }
      )
    }

    // Check if course name is unique (if name is being updated)
    if (validatedData.name && validatedData.name !== existingCourse.name) {
      const courseWithSameName = await prisma.course.findUnique({
        where: { name: validatedData.name },
      })

      if (courseWithSameName) {
        return NextResponse.json(
          { error: 'Course name already exists' },
          { status: 400 }
        )
      }
    }

    const course = await prisma.course.update({
      where: { id },
      data: {
        ...validatedData,
        updatedAt: new Date(),
      },
      include: {
        _count: {
          select: {
            groups: true,
          },
        },
      },
    })

    return NextResponse.json(course)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating course:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    // Check if course exists
    const existingCourse = await prisma.course.findUnique({
      where: { id },
      include: {
        groups: {
          include: {
            enrollments: true,
          },
        },
      },
    })

    if (!existingCourse) {
      return NextResponse.json(
        { error: 'Course not found' },
        { status: 404 }
      )
    }

    // Check if course has groups
    if (existingCourse.groups.length > 0) {
      // Check if any groups have active enrollments
      const hasActiveEnrollments = existingCourse.groups.some(group =>
        group.enrollments.some(enrollment => enrollment.status === 'ACTIVE')
      )

      if (hasActiveEnrollments) {
        return NextResponse.json(
          { 
            error: 'Cannot delete course with active enrollments',
            details: 'Course has groups with active student enrollments'
          },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { 
          error: 'Cannot delete course with existing groups',
          details: `Course has ${existingCourse.groups.length} group(s). Please delete groups first.`
        },
        { status: 400 }
      )
    }

    // Delete course
    await prisma.course.delete({
      where: { id },
    })

    return NextResponse.json({
      message: 'Course deleted successfully',
      deletedId: id
    })
  } catch (error) {
    console.error('Error deleting course:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
