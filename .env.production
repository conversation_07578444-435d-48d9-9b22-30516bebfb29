# Production Environment Variables for Vercel Deployment - STAFF SERVER
# Copy these to your Vercel project environment variables

# Database Configuration - Staff Database
DATABASE_URL="postgresql://crm-staff_owner:<EMAIL>/crm-staff?sslmode=require&channel_binding=require"

# NextAuth.js Configuration
NEXTAUTH_SECRET="inno-crm-staff-super-secret-key-for-production-2024-very-long-and-secure"
NEXTAUTH_URL="https://inno-crm-staff.vercel.app"

# Prisma Configuration for Vercel
PRISMA_GENERATE_DATAPROXY="true"

# Application Configuration - Admin Server
APP_NAME="Innovative Centre - Admin Portal"
APP_URL="https://inno-crm-admin.vercel.app"
APP_ENV="production"
SERVER_TYPE="admin"

# Inter-Server Communication
ADMIN_SERVER_URL="https://inno-crm-admin.vercel.app"
STAFF_SERVER_URL="https://inno-crm-staff.vercel.app"
INTER_SERVER_SECRET="inno-crm-inter-server-communication-secret-2024"

# SMS Service Configuration
SMS_PROVIDER="eskiz"
SMS_API_KEY="your-production-sms-api-key"

# Eskiz.uz Configuration
ESKIZ_API_URL="https://notify.eskiz.uz"
ESKIZ_FROM="4546"

# Email Service Configuration
EMAIL_PROVIDER="gmail"
EMAIL_USER="<EMAIL>"
EMAIL_PASSWORD="your-production-app-password"
EMAIL_FROM="Innovative Centre <<EMAIL>>"

# SMTP Configuration
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_SECURE="false"

# Security Configuration
BCRYPT_ROUNDS="12"
JWT_EXPIRES_IN="7d"
SESSION_MAX_AGE="604800"

# Performance Configuration
PERFORMANCE_MONITORING="true"
SLOW_QUERY_THRESHOLD="1000"
MEMORY_USAGE_ALERT_THRESHOLD="80"

# Feature Flags - Staff Server (Limited Features)
FEATURE_SMS_ENABLED="true"
FEATURE_EMAIL_ENABLED="true"
FEATURE_WORKFLOWS_ENABLED="true"
FEATURE_ANALYTICS_ENABLED="false"
FEATURE_REPORTS_ENABLED="false"
FEATURE_BULK_OPERATIONS="true"
FEATURE_ADMIN_PANEL="false"
FEATURE_FINANCIAL_REPORTS="false"
FEATURE_KPI_TRACKING="false"

# Uzbekistan Specific Configuration
TIMEZONE="Asia/Tashkent"
CURRENCY="UZS"
LOCALE="uz-UZ"
PHONE_COUNTRY_CODE="+998"

# Branch Configuration
DEFAULT_BRANCH="Main"
BRANCHES="Main,Chilonzor,Yunusobod"

# Course Configuration
DEFAULT_COURSE_DURATION="3"
DEFAULT_CLASS_DURATION="90"
MAX_STUDENTS_PER_GROUP="15"

# Payment Configuration
PAYMENT_GRACE_PERIOD="7"
LATE_PAYMENT_FEE="50000"
PAYMENT_REMINDER_DAYS="3,7,14"

# Logging Configuration
LOG_LEVEL="info"
LOG_FILE_ENABLED="false"

# Cache Configuration
CACHE_TTL="300"
CACHE_MAX_SIZE="1000"

# Rate Limiting
RATE_LIMIT_ENABLED="true"
RATE_LIMIT_MAX_REQUESTS="100"
RATE_LIMIT_WINDOW_MS="900000"

# Notification Configuration
NOTIFICATION_BATCH_SIZE="50"
NOTIFICATION_RETRY_ATTEMPTS="3"
NOTIFICATION_RETRY_DELAY="5000"

# Workflow Configuration
WORKFLOW_ENABLED="true"
WORKFLOW_CHECK_INTERVAL="300000"
WORKFLOW_MAX_RETRIES="3"

# Monitoring Configuration
HEALTH_CHECK_ENABLED="true"
HEALTH_CHECK_INTERVAL="60000"
UPTIME_MONITORING="true"

# Backup Configuration (disabled in production)
BACKUP_ENABLED="false"
AUTO_BACKUP_ENABLED="false"

# Development Configuration (disabled in production)
DEBUG="false"
VERBOSE_LOGGING="false"
MOCK_SMS="false"
MOCK_EMAIL="false"
