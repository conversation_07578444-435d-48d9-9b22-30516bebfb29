// Inter-Server Health Check Endpoint
// Allows servers to check each other's status

import { NextRequest, NextResponse } from 'next/server';
import { validateInterServerAuth, InterServerUtils } from '@/lib/inter-server';

export async function GET(request: NextRequest) {
  try {
    // Validate inter-server authentication
    if (!validateInterServerAuth(request)) {
      return NextResponse.json(
        { error: 'Unauthorized inter-server request' },
        { status: 401 }
      );
    }

    // Log the health check request
    InterServerUtils.logRequest('incoming', '/api/inter-server/health', true);

    // Return server status
    const serverConfig = InterServerUtils.getServerConfig();
    
    return NextResponse.json({
      status: 'healthy',
      server: serverConfig.serverType,
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.APP_ENV || 'development',
    });
  } catch (error) {
    console.error('Inter-server health check error:', error);
    
    return NextResponse.json(
      { 
        error: 'Health check failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  // Handle detailed health check with diagnostics
  try {
    if (!validateInterServerAuth(request)) {
      return NextResponse.json(
        { error: 'Unauthorized inter-server request' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { includeDetails = false } = body;

    const serverConfig = InterServerUtils.getServerConfig();
    
    const healthData: any = {
      status: 'healthy',
      server: serverConfig.serverType,
      timestamp: new Date().toISOString(),
    };

    if (includeDetails) {
      // Add detailed diagnostics
      healthData.details = {
        database: 'connected', // You can add actual DB check here
        memory: process.memoryUsage(),
        uptime: process.uptime(),
        nodeVersion: process.version,
        environment: process.env.NODE_ENV,
      };
    }

    InterServerUtils.logRequest('incoming', '/api/inter-server/health', true, { includeDetails });

    return NextResponse.json(healthData);
  } catch (error) {
    console.error('Detailed health check error:', error);
    
    return NextResponse.json(
      { 
        error: 'Detailed health check failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
