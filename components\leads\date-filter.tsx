'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent } from '@/components/ui/card'
import { Calendar, ChevronDown, X } from 'lucide-react'
import { formatDate } from '@/lib/utils'

interface DateFilterProps {
  onFilterChange: (filter: {
    dateFilter?: string
    startDate?: string
    endDate?: string
  }) => void
  currentFilter?: string
}

const presetFilters = [
  { value: 'today', label: 'Today' },
  { value: 'yesterday', label: 'Yesterday' },
  { value: 'last7days', label: 'Last 7 days' },
  { value: 'last30days', label: 'Last 30 days' },
  { value: 'custom', label: 'Custom range' },
]

export default function DateFilter({ onFilterChange, currentFilter }: DateFilterProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [selectedFilter, setSelectedFilter] = useState(currentFilter || '')
  const [customStartDate, setCustomStartDate] = useState('')
  const [customEndDate, setCustomEndDate] = useState('')
  const [showCustomInputs, setShowCustomInputs] = useState(false)

  const handleFilterSelect = (filterValue: string) => {
    setSelectedFilter(filterValue)
    
    if (filterValue === 'custom') {
      setShowCustomInputs(true)
      return
    }
    
    setShowCustomInputs(false)
    onFilterChange({ dateFilter: filterValue })
    setIsOpen(false)
  }

  const handleCustomDateApply = () => {
    if (customStartDate && customEndDate) {
      onFilterChange({
        startDate: customStartDate,
        endDate: customEndDate
      })
      setIsOpen(false)
      setShowCustomInputs(false)
    }
  }

  const clearFilter = () => {
    setSelectedFilter('')
    setCustomStartDate('')
    setCustomEndDate('')
    setShowCustomInputs(false)
    onFilterChange({})
    setIsOpen(false)
  }

  const getDisplayText = () => {
    if (!selectedFilter) return 'All time'
    
    const preset = presetFilters.find(f => f.value === selectedFilter)
    if (preset && preset.value !== 'custom') {
      return preset.label
    }
    
    if (customStartDate && customEndDate) {
      return `${formatDate(customStartDate)} - ${formatDate(customEndDate)}`
    }
    
    return 'Select date range'
  }

  return (
    <div className="relative">
      <Button
        variant="outline"
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 min-w-[200px] justify-between"
      >
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4" />
          <span className="truncate">{getDisplayText()}</span>
        </div>
        <ChevronDown className="h-4 w-4" />
      </Button>

      {isOpen && (
        <Card className="absolute top-full left-0 mt-1 z-50 min-w-[300px]">
          <CardContent className="p-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">Filter by date</h4>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsOpen(false)}
                  className="h-6 w-6 p-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <div className="space-y-2">
                {presetFilters.map((filter) => (
                  <Button
                    key={filter.value}
                    variant={selectedFilter === filter.value ? 'default' : 'ghost'}
                    className="w-full justify-start"
                    onClick={() => handleFilterSelect(filter.value)}
                  >
                    {filter.label}
                  </Button>
                ))}
              </div>

              {showCustomInputs && (
                <div className="space-y-3 pt-3 border-t">
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <Label htmlFor="start-date" className="text-xs">Start Date</Label>
                      <Input
                        id="start-date"
                        type="date"
                        value={customStartDate}
                        onChange={(e) => setCustomStartDate(e.target.value)}
                        className="text-sm"
                      />
                    </div>
                    <div>
                      <Label htmlFor="end-date" className="text-xs">End Date</Label>
                      <Input
                        id="end-date"
                        type="date"
                        value={customEndDate}
                        onChange={(e) => setCustomEndDate(e.target.value)}
                        className="text-sm"
                      />
                    </div>
                  </div>
                  <Button
                    onClick={handleCustomDateApply}
                    disabled={!customStartDate || !customEndDate}
                    className="w-full"
                    size="sm"
                  >
                    Apply Custom Range
                  </Button>
                </div>
              )}

              {selectedFilter && (
                <div className="pt-3 border-t">
                  <Button
                    variant="outline"
                    onClick={clearFilter}
                    className="w-full"
                    size="sm"
                  >
                    Clear Filter
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
