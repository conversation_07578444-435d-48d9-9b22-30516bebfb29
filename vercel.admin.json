{"buildCommand": "prisma generate && next build", "installCommand": "npm install", "framework": "nextjs", "regions": ["iad1"], "functions": {"app/api/**/*.ts": {"maxDuration": 30}}, "env": {"PRISMA_GENERATE_DATAPROXY": "true"}, "build": {"env": {"PRISMA_GENERATE_DATAPROXY": "true"}}, "rewrites": [{"source": "/api/(.*)", "destination": "/api/$1"}], "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "https://inno-crm-staff.vercel.app"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization, X-Inter-Server-Secret"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}]}, {"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains"}]}]}