import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🚀 Adding Academic Manager to reception database...')

  // Create Academic Manager
  const academicPassword = await bcrypt.hash('academic123', 10)
  const academicUser = await prisma.user.upsert({
    where: { phone: '+998903333333' },
    update: {
      password: academicPassword,
      role: 'ACADEMIC_MANAGER'
    },
    create: {
      phone: '+998903333333',
      name: 'Academic Manager',
      email: '<EMAIL>',
      role: 'ACADEMIC_MANAGER',
      password: academicPassword,
    },
  })

  console.log('✅ Academic Manager added successfully!')
  console.log('\n🔐 RECEPTION SERVER ADDITIONAL CREDENTIAL:')
  console.log('   Academic Manager: +998903333333 / academic123')
  
  console.log('\n🎉 Reception database is ready for testing!')
}

main()
  .catch((e) => {
    console.error('❌ Error adding academic manager:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
